service: auth-clear-functions

plugins:
  - serverless-dotenv-plugin
  - serverless-iam-roles-per-function
  - serverless-export-env
  - serverless-offline

provider:
  name: aws
  runtime: nodejs20.x
  stage: ${opt:stage, 'dev'}
  region: us-east-1
  timeout: 30
  environment:
    STAGE: ${self:provider.stage}
    PAYRIX_API_URL: ${env:PAYRIX_API_URL, 'https://test-api.payrix.com'}
    PAYRIX_PRIVATE_API_KEY: ${env:PAYRIX_PRIVATE_API_KEY, 'default-key'}
    PAYRIX_PUBLIC_API_KEY: ${env:PAYRIX_PUBLIC_API_KEY, 'default-public-key'}
    PAYMENT_TOKENS_TABLE_NAME: !Ref PaymentTokensTable
    MERCHANT_DATA_TABLE_NAME: !Ref MerchantDataTable
  
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - ssm:GetParameter
            - ssm:GetParameters
          Resource:
            - "arn:aws:ssm:${self:provider.region}:*:parameter/*"
        - Effect: Allow
          Action:
            - dynamodb:PutItem
            - dynamodb:GetItem
            - dynamodb:DeleteItem
          Resource:
            - !GetAtt PaymentTokensTable.Arn
        - Effect: Allow
          Action:
            - dynamodb:PutItem
            - dynamodb:GetItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
          Resource:
            - !GetAtt MerchantDataTable.Arn

build:
  esbuild:
    bundle: true
    minify: false
    sourcemap: true
    target: node20
    format: cjs
    external:
      - '@aws-sdk/*'
      - 'pg-native'
      - '@mapbox/node-pre-gyp'
      - 'mock-aws-s3'
      - 'aws-sdk'
      - 'pg'
      - 'bcrypt'

functions:
  onboardMerchant:
    handler: src/functions/merchants/onboard.handler
    events:
      - http:
          path: /merchants/onboard
          method: post
          cors:
            origin: ${self:custom.corsOrigins}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true
            methods:
              - POST
              - OPTIONS

  createNote:
    handler: src/functions/merchants/create-note.handler
    events:
      - http:
          path: /merchants/notes
          method: post
          cors:
            origin: ${self:custom.corsOrigins}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true
            methods:
              - POST
              - OPTIONS

  createNoteDocument:
    handler: src/functions/merchants/create-note-document.handler
    events:
      - http:
          path: /merchants/note-documents
          method: post
          cors:
            origin: ${self:custom.corsOrigins}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true
            methods:
              - POST
              - OPTIONS

  createPlaidLinkToken:
    handler: src/functions/merchants/create-plaid-link-token.handler
    events:
      - http:
          path: /merchants/plaid/link-token
          method: post
          cors:
            origin: ${self:custom.corsOrigins}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true
            methods:
              - POST
              - OPTIONS

  processPlaidAccount:
    handler: src/functions/merchants/process-plaid-account.handler
    events:
      - http:
          path: /merchants/plaid/process-account
          method: post
          cors:
            origin: ${self:custom.corsOrigins}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true
            methods:
              - POST
              - OPTIONS

  processTokenPayment:
    handler: src/functions/payments/process-token-payment.handler
    events:
      - http:
          path: /payments/process-token-payment
          method: post
          cors:
            origin: ${self:custom.corsOrigins}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true
            methods:
              - POST
              - OPTIONS

  generateIntegrationToken:
    handler: src/functions/payments/generate-integration-token.handler
    environment:
      FRONTEND_URL: ${env:FRONTEND_URL, 'https://${cf:auth-clear-frontend-${self:provider.stage}.CloudFrontDomainName}'}
    events:
      - http:
          path: /payments/generate-integration-token
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - POST
              - OPTIONS

  cleanupIntegrationToken:
    handler: src/functions/payments/generate-integration-token.cleanupHandler
    events:
      - http:
          path: /payments/cleanup-integration-token
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - POST
              - OPTIONS

  validateIframeToken:
    handler: src/functions/payments/validate-iframe-token.handler
    events:
      - http:
          path: /payments/validate-iframe-token
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - POST
              - OPTIONS



  tokenStatus:
    handler: src/functions/payments/token-status.handler
    events:
      - http:
          path: /payments/token-status
          method: get
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - GET
              - POST
              - OPTIONS
      - http:
          path: /payments/token-status
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
            methods:
              - GET
              - POST
              - OPTIONS

custom:
  corsOrigins: "https://${cf:auth-clear-frontend-${self:provider.stage}.CloudFrontDomainName},http://localhost:3000,http://localhost:8080"
  dotenv:
    path: ${self:custom.dotenvPath.${self:provider.stage}, self:custom.dotenvPath.default}
  dotenvPath:
    dev: .env
    prod: .env.production
    default: .env
  exportEnv:
    overwrite: true
    filename: ../.env
  serverless-offline:
    stage: ${self:provider.stage}
    region: ${self:provider.region}
    awsProfile: payrix

package:
  individually: true

resources:
  Resources:
    PaymentTokensTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: PaymentTokens-${self:provider.stage}
        AttributeDefinitions:
          - AttributeName: tokenId
            AttributeType: S
        KeySchema:
          - AttributeName: tokenId
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TimeToLiveSpecification:
          AttributeName: expiresAt
          Enabled: true
        Tags:
          - Key: Environment
            Value: ${self:provider.stage}
          - Key: Service
            Value: auth-clear-functions
          - Key: Purpose
            Value: payment-token-storage

    MerchantDataTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: MerchantData-${self:provider.stage}
        AttributeDefinitions:
          - AttributeName: merchantId
            AttributeType: S
        KeySchema:
          - AttributeName: merchantId
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TimeToLiveSpecification:
          AttributeName: ttl
          Enabled: true
        Tags:
          - Key: Environment
            Value: ${self:provider.stage}
          - Key: Service
            Value: auth-clear-functions
          - Key: Purpose
            Value: merchant-data-storage

  Outputs:
    PaymentTokensTableName:
      Description: Name of the DynamoDB table for payment tokens
      Value: !Ref PaymentTokensTable
      Export:
        Name: ${self:service}-${self:provider.stage}-PaymentTokensTableName

    PaymentTokensTableArn:
      Description: ARN of the DynamoDB table for payment tokens
      Value: !GetAtt PaymentTokensTable.Arn
      Export:
        Name: ${self:service}-${self:provider.stage}-PaymentTokensTableArn

    MerchantDataTableName:
      Description: Name of the DynamoDB table for merchant data
      Value: !Ref MerchantDataTable
      Export:
        Name: ${self:service}-${self:provider.stage}-MerchantDataTableName

    MerchantDataTableArn:
      Description: ARN of the DynamoDB table for merchant data
      Value: !GetAtt MerchantDataTable.Arn
      Export:
        Name: ${self:service}-${self:provider.stage}-MerchantDataTableArn